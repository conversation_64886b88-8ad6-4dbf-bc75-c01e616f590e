<?xml version="1.0" encoding="UTF-8"?>
<web-app version="3.1" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <display-name>TreinoWeb</display-name>

    <context-param>
        <param-name>com.sun.faces.sendPoweredByHeader</param-name>
        <param-value>false</param-value>
    </context-param>

    <context-param>
        <param-name>javax.faces.PROJECT_STAGE</param-name>
        <param-value>Production</param-value>
    </context-param>
    <welcome-file-list>
        <welcome-file>login.xhtml</welcome-file>
    </welcome-file-list>
    <mime-mapping>
        <extension>ttf</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>otf</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <listener>
        <listener-class>com.sun.xml.ws.transport.http.servlet.WSServletContextListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>Faces Servlet</servlet-name>
        <servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <filter-mapping>
        <filter-name>CredentialFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>Pretty Filter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>FORWARD</dispatcher>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>ERROR</dispatcher>
    </filter-mapping>
    <servlet>
        <servlet-name>OamdWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>/act/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>*.psg</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>*.xhtml</url-pattern>
    </servlet-mapping>
    <context-param>
        <description>State saving method: 'client' or 'server' (=default). See JSF Specification 2.5.2</description>
        <param-name>javax.faces.STATE_SAVING_METHOD</param-name>
        <param-value>server</param-value>
    </context-param>
    <context-param>
        <param-name>javax.servlet.jsp.jstl.fmt.localizationContext</param-name>
        <param-value>resources.application</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.THEME</param-name>
        <param-value>bootstrap</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.PRIVATE_CAPTCHA_KEY</param-name>
        <param-value>6LfAy_MSAAAAAMJVEbOwsbdkIoLjaUq-Zz4AB9g6</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.PUBLIC_CAPTCHA_KEY</param-name>
        <param-value>6LfAy_MSAAAAAN0lB6LdipxLIKtwj6F6B2BvuyxK</param-value>
    </context-param>
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>/WEB-INF/applicationContext.xml</param-value>
    </context-param>
    <context-param>
        <param-name>primefaces.UPLOADER</param-name>
        <param-value>commons</param-value>
    </context-param>
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <listener>
        <listener-class>com.sun.faces.config.ConfigureListener</listener-class>
    </listener>
     <listener>
        <listener-class>br.com.pacto.controller.oamd.OamdContextListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>mvc-dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>mvc-dispatcher</servlet-name>
        <url-pattern>/prest/*</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>QRCode</servlet-name>
        <servlet-class>br.com.pacto.controller.servlet.QRCodeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>QRCode</servlet-name>
        <url-pattern>/QRCode</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>OamdWS</servlet-name>
        <url-pattern>/OamdWS</url-pattern>
    </servlet-mapping>
    <session-config>
        <session-timeout>480</session-timeout>
    </session-config>
    <filter>
        <filter-name>Pretty Filter</filter-name>
        <filter-class>com.ocpsoft.pretty.PrettyFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter>
        <filter-name>CredentialFilter</filter-name>
        <filter-class>br.com.pacto.controller.filter.CredentialFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter-mapping>
        <filter-name>CrossApplicationFilter</filter-name>
        <url-pattern>/prest/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>SwaggerRedirectFilter</filter-name>
        <url-pattern>/swagger-resources/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>SwaggerRedirectFilter</filter-name>
        <url-pattern>/v2/api-docs</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>CrossApplicationFilter</filter-name>
        <filter-class>br.com.pacto.filter.CrossApplicationFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter>
        <filter-name>SwaggerRedirectFilter</filter-name>
        <filter-class>br.com.pacto.swagger.SwaggerRedirectFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
<!--    filtro de api-->
    <filter>
        <filter-name>RequisicaoFilter</filter-name>
        <filter-class>br.com.pacto.controller.filter.RequisicaoFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter-mapping>
        <filter-name>RequisicaoFilter</filter-name>
        <url-pattern>/prest/*</url-pattern>
    </filter-mapping>
<!--    filtro de api-->
    <filter>
        <filter-name>PrimeFaces FileUpload Filter</filter-name>
        <filter-class>
            org.primefaces.webapp.filter.FileUploadFilter
        </filter-class>
        <init-param>
            <param-name>thresholdSize</param-name>
            <param-value>51200</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>PrimeFaces FileUpload Filter</filter-name>
        <servlet-name>Faces Servlet</servlet-name>
        <dispatcher>FORWARD</dispatcher>
        <dispatcher>REQUEST</dispatcher>
    </filter-mapping>
    <!-- ATMOSPHERE -->
    <servlet>
        <description>AtmosphereServlet</description>
        <servlet-name>AtmosphereServlet</servlet-name>
        <servlet-class>org.atmosphere.cpr.AtmosphereServlet</servlet-class>
        <!-- Use it with mvn jetty:run -->
        <load-on-startup>0</load-on-startup>
        <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
        <servlet-name>AtmosphereServlet</servlet-name>
        <url-pattern>/chat/*</url-pattern>
    </servlet-mapping>
    <!-- -->
    <servlet>
        <servlet-name>TXTServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.TXTServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TXTServlet</servlet-name>
        <url-pattern>/txt</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>OIDServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.OIDServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OIDServlet</servlet-name>
        <url-pattern>/oid</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>OIDServlet</servlet-name>
        <url-pattern>/oid2</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImageServletRedeSocial</servlet-name>
        <servlet-class>br.com.pacto.servlet.ImageServletRedeSocial</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImageServletRedeSocial</servlet-name>
        <url-pattern>/imagenslogoapp</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>RedirectServlet</servlet-name>
        <servlet-class>br.com.pacto.servlet.RedirectServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RedirectServlet</servlet-name>
        <url-pattern>/redir</url-pattern>
    </servlet-mapping>
    <!--
    <listener>
        <listener-class>com.sun.xml.ws.transport.http.servlet.WSServletContextListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>OamdWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>OamdWS</servlet-name>
        <url-pattern>/OamdWS</url-pattern>
    </servlet-mapping>
    -->
</web-app>
