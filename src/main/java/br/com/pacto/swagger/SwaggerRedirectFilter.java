package br.com.pacto.swagger;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Filtro para redirecionar requisições do Swagger para o contexto correto com prefixo /prest
 * Este filtro intercepta chamadas feitas sem o prefixo /prest e as redireciona para o contexto correto.
 */
public class SwaggerRedirectFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Inicialização do filtro - não é necessária nenhuma configuração especial
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String requestURI = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        
        // Remove o context path da URI para obter apenas o path relativo
        String relativePath = requestURI.substring(contextPath.length());
        
        // Verifica se é uma requisição do Swagger que precisa ser redirecionada
        if (isSwaggerResource(relativePath)) {
            String redirectURL = contextPath + "/prest" + relativePath;
            httpResponse.sendRedirect(redirectURL);
            return;
        }
        
        // Se não é uma requisição do Swagger, continua o processamento normal
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        // Limpeza do filtro - não é necessária nenhuma limpeza especial
    }

    /**
     * Verifica se a requisição é para um recurso do Swagger que precisa ser redirecionado
     */
    private boolean isSwaggerResource(String path) {
        return path.startsWith("/swagger-resources") || 
               path.equals("/v2/api-docs") ||
               path.startsWith("/v2/api-docs/");
    }
}
