package br.com.pacto.swagger;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Controlador para redirecionar requisições do Swagger para o contexto correto com prefixo /prest
 * Este controlador intercepta chamadas feitas sem o prefixo /prest e as redireciona para o contexto correto.
 */
@Controller
public class SwaggerRedirectController {

    /**
     * Redireciona requisições de configuração da UI do Swagger
     */
    @RequestMapping(value = "/swagger-resources/configuration/ui", method = RequestMethod.GET)
    public void redirectConfigurationUi(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String contextPath = request.getContextPath();
        response.sendRedirect(contextPath + "/prest/swagger-resources/configuration/ui");
    }

    /**
     * Redireciona requisições de configuração de segurança do Swagger
     */
    @RequestMapping(value = "/swagger-resources/configuration/security", method = RequestMethod.GET)
    public void redirectConfigurationSecurity(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String contextPath = request.getContextPath();
        response.sendRedirect(contextPath + "/prest/swagger-resources/configuration/security");
    }

    /**
     * Redireciona requisições de recursos do Swagger
     */
    @RequestMapping(value = "/swagger-resources", method = RequestMethod.GET)
    public void redirectSwaggerResources(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String contextPath = request.getContextPath();
        response.sendRedirect(contextPath + "/prest/swagger-resources");
    }

    /**
     * Redireciona requisições da documentação da API
     */
    @RequestMapping(value = "/v2/api-docs", method = RequestMethod.GET)
    public void redirectApiDocs(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String contextPath = request.getContextPath();
        response.sendRedirect(contextPath + "/prest/v2/api-docs");
    }
}
