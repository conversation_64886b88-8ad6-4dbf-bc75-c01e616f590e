package br.com.pacto.swagger;

import com.amazonaws.handlers.RequestHandler;
import com.google.common.base.Predicate;
import org.hibernate.type.TypeResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Configuração do Swagger para documentação da API REST.
 *
 * Esta classe configura o Spring Fox para gerar automaticamente a documentação
 * dos endpoints REST da aplicação OAMD (Online Academy Management Dashboard).
 *
 * <AUTHOR> Pacto
 * @version 1.0
 */


@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Autowired
    private TypeResolver typeResolver;

    @Bean
    public Docket api() {
        Predicate<RequestHandler> documentedOnly = input -> {
            return input.isAnnotatedWith(ApiOperation.class);
        };

        return new Docket(DocumentationType.SWAGGER_2)
                .securitySchemes(Collections.singletonList(apiKey()))
                .securityContexts(Collections.singletonList(securityContext()))
                .globalResponseMessage(RequestMethod.GET, getGlobalResponseMessages())
                .globalResponseMessage(RequestMethod.POST, getGlobalResponseMessages())
                .globalResponseMessage(RequestMethod.PUT, getGlobalResponseMessages())
                .globalResponseMessage(RequestMethod.DELETE, getGlobalResponseMessages())
                .select()
                .apis(RequestHandlerSelectors.basePackage("br.com.pacto.controller"))
                .apis(documentedOnly)
                .paths(PathSelectors.any())
                .build()
                .apiInfo(new ApiInfoBuilder()
                        .title("OAMD")
                        .description("Documentação do OAMD")
                        .version("1.0")
                        .build())
                .globalOperationParameters(globalHeaders);
    }


    private ApiKey apiKey() {
        return new ApiKey("Authorization", "Authorization", "header");
    }

    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(PathSelectors.any())
                .build();
    }

    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope =
                new AuthorizationScope("global", "Acesso total à API");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[]{authorizationScope};
        return Arrays.asList(new SecurityReference("Authorization", authorizationScopes));
    }
}
